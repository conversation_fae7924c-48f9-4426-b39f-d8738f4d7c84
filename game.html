<!DOCTYPE html> <html lang="en"> <head> <meta charset="utf-8" /> <meta name="viewport" content="width=device-width, initial-scale=1.0"/> <title>Space Shooter FX - Single File</title> <style> html, body { margin: 0; padding: 0; background: #05070c; color: #e8ecf1; font-family: system-ui, Arial, sans-serif; overflow: hidden; } #ui { position: absolute; top: 8px; left: 8px; z-index: 10; font-size: 14px; text-shadow: 0 0 4px #000; pointer-events: none; } #ui .row { margin: 4px 0; } #centerText { position: absolute; top: 50%; left: 50%; width: 90%; max-width: 800px; transform: translate(-50%, -50%); text-align: center; z-index: 20; text-shadow: 0 0 8px #000; } #centerText h1 { margin: 0 0 8px 0; font-weight: 700; } #centerText p { margin: 8px 0; } #centerText .btn { display: inline-block; background: #1f6feb; color: white; border-radius: 6px; padding: 10px 18px; margin-top: 12px; cursor: pointer; user-select: none; } #centerText .btn:hover { background: #2a7bf6; } canvas { display: block; } #pauseMenu { position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: rgba(0, 0, 0, 0.9); border: 2px solid #1f6feb; border-radius: 10px; padding: 30px; text-align: center; display: none; z-index: 30; } #pauseMenu h2 { margin: 0 0 20px 0; color: #e8ecf1; } #pauseMenu .btn { display: block; background: #1f6feb; color: white; border-radius: 6px; padding: 10px 18px; margin: 10px auto; cursor: pointer; width: 150px; user-select: none; } #pauseMenu .btn:hover { background: #2a7bf6; } #highScore { position: absolute; top: 8px; right: 8px; z-index: 10; font-size: 14px; text-shadow: 0 0 4px #000; } #comboDisplay { position: absolute; top: 50px; left: 50%; transform: translateX(-50%); font-size: 24px; font-weight: bold; color: #FFD700; text-shadow: 0 0 8px #000; opacity: 0; transition: opacity 0.3s; z-index: 15; } </style> </head> <body> <div id="ui"> <div class="row">Score: <span id="score">0</span></div> <div class="row">Lives: <span id="lives">3</span></div> <div class="row">Weapon: <span id="weapon">Cannon</span></div> <div class="row">Level: <span id="level">1</span></div> </div> <div id="highScore">High Score: <span id="highScoreValue">0</span></div> <div id="comboDisplay">COMBO x<span id="comboValue">0</span></div> <div id="pauseMenu"> <h2>Game Paused</h2> <div class="btn" id="resumeBtn">Resume</div> <div class="btn" id="restartFromPauseBtn">Restart</div> <div class="btn" id="mainMenuBtn">Main Menu</div> </div> <div id="centerText"> <h1>Space Shooter FX</h1> <p>Controls: Arrow keys / WASD to move. Space to shoot. Shift for boost. P to pause.</p> <p>Enhanced special effects: screen shake, bloom glow, heat haze, trails, and more.</p> <div class="btn" id="startBtn">Start Game</div> </div> <canvas id="game"></canvas> <script> (function() { const canvas = document.getElementById('game'); const ctx = canvas.getContext('2d', { alpha: false }); let W = window.innerWidth, H = window.innerHeight; canvas.width = W; canvas.height = H; const scoreEl = document.getElementById('score'); const livesEl = document.getElementById('lives'); const weaponEl = document.getElementById('weapon'); const levelEl = document.getElementById('level'); const startBtn = document.getElementById('startBtn'); const centerText = document.getElementById('centerText'); let running = false; let paused = false; let lastTime = 0; let time = 0; let score = 0; let level = 1; let lives = 3; let highScore = localStorage.getItem('spaceShooterHighScore') || 0; let combo = 0; let comboTimer = 0; const keys = {}; window.addEventListener('keydown', (e) => { keys[e.key.toLowerCase()] = true; if (e.key === ' ' || e.code === 'Space') keys[' '] = true; if (e.key.toLowerCase() === 'p') togglePause(); }); window.addEventListener('keyup', (e) => { keys[e.key.toLowerCase()] = false; if (e.key === ' ' || e.code === 'Space') keys[' '] = false; }); window.addEventListener('resize', () => { W = window.innerWidth; H = window.innerHeight; canvas.width = W; canvas.height = H; resizeOffscreen(); }); function togglePause() { if (!running) return; paused = !paused; const pauseMenu = document.getElementById('pauseMenu'); if (paused) { pauseMenu.style.display = 'block'; } else { pauseMenu.style.display = 'none'; lastTime = performance.now(); requestAnimationFrame(loop); } } // WebAudio const audioCtx = new (window.AudioContext || window.webkitAudioContext)(); let masterGain = audioCtx.createGain(); masterGain.gain.value = 0.2; masterGain.connect(audioCtx.destination); function beep({freq=440, type='square', duration=0.08, attack=0.005, release=0.08, detune=0, pan=0}) { const osc = audioCtx.createOscillator(); const gain = audioCtx.createGain(); const panner = (audioCtx.createStereoPanner) ? audioCtx.createStereoPanner() : null; osc.type = type; osc.frequency.value = freq; osc.detune.value = detune; gain.gain.value = 0; if (panner) panner.pan.value = pan; osc.connect(gain); if (panner) { gain.connect(panner); panner.connect(masterGain); } else { gain.connect(masterGain); } const now = audioCtx.currentTime; gain.gain.cancelScheduledValues(now); gain.gain.setValueAtTime(0, now); gain.gain.linearRampToValueAtTime(0.5, now + attack); gain.gain.linearRampToValueAtTime(0.0, now + attack + release); osc.start(now); osc.stop(now + duration + 0.05); } function shootSound() { beep({freq: 920, type:'sawtooth', duration:0.06, attack:0.002, release:0.06}); } function powerupSound() { beep({freq: 680, type:'triangle', duration:0.12, attack:0.005, release:0.14}); } function hitSound() { beep({freq: 220, type:'square', duration:0.07, attack:0.003, release:0.09}); } function explodeSound() { beep({freq: 80, type:'sawtooth', duration:0.22, attack:0.003, release:0.22, detune: -1200}); beep({freq: 130, type:'square', duration:0.18, attack:0.003, release:0.18, detune: -800}); } // Offscreen buffers for post FX let glowCanvas = document.createElement('canvas'); let glowCtx = glowCanvas.getContext('2d'); let heatCanvas = document.createElement('canvas'); let heatCtx = heatCanvas.getContext('2d'); let motionCanvas = document.createElement('canvas'); let motionCtx = motionCanvas.getContext('2d'); function resizeOffscreen() { glowCanvas.width = W; glowCanvas.height = H; heatCanvas.width = W; heatCanvas.height = H; motionCanvas.width = W; motionCanvas.height = H; } resizeOffscreen(); // Starfield layers function makeStars(count, speedMin, speedMax) { const arr = []; for (let i=0;i<count;i++){ arr.push({ x: Math.random()*W, y: Math.random()*H, z: Math.random(), s: Math.random()*1.8+0.3, v: speedMin + Math.random()*(speedMax-speedMin), tw: Math.random()*Math.PI*2 }); } return arr; } const starsFar = makeStars(160, 8, 18); const starsMid = makeStars(130, 18, 38); const starsNear = makeStars(100, 38, 70); const dust = Array.from({length: 70}, ()=>({ x: Math.random()*W, y: Math.random()*H, r: Math.random()*2+0.5, a: Math.random()*0.2+0.05 })); // Player const player = { x: W*0.5, y: H*0.75, vx: 0, vy: 0, speed: 420, boostMult: 1.7, friction: 0.9, w: 64, h: 64, fireCooldown: 0, fireRate: 0.16, weaponLevel: 1, invuln: 0, heat: 0 }; const bullets = []; const enemyBullets = []; const enemies = []; const explosions = []; const powerups = []; const sparks = []; // hit sparks const trails = []; // motion trails const particles = []; // general particles let spawnTimer = 0; let spawnInterval = 1.1; // Screen shake let shakeTime = 0; let shakeAmp = 0; function addShake(power=6, duration=0.25) { shakeAmp = Math.max(shakeAmp, power); shakeTime = Math.max(shakeTime, duration); } // Plane graphic let planeCanvas = document.createElement('canvas'); let planeReady = false; function createPlaneGraphic() { const pw = 160, ph = 160; planeCanvas.width = pw; planeCanvas.height = ph; const pctx = planeCanvas.getContext('2d'); const svgPath = new Path2D("M80 5 L90 45 L110 55 L155 60 L115 70 L100 75 L100 110 L120 130 L110 135 L95 125 L80 150 L65 125 L50 135 L40 130 L60 110 L60 75 L45 70 L5 60 L50 55 L70 45 Z"); let grad = pctx.createLinearGradient(0,0,0,ph); grad.addColorStop(0, '#d8e1ea'); grad.addColorStop(0.5, '#8a99ad'); grad.addColorStop(1, '#c7d1dd'); pctx.fillStyle = grad; pctx.strokeStyle = '#2c3e50'; pctx.lineWidth = 2; pctx.shadowColor = 'rgba(0,0,0,0.25)'; pctx.shadowBlur = 8; pctx.shadowOffsetY = 3; pctx.fill(svgPath); pctx.shadowColor = 'transparent'; pctx.stroke(svgPath); pctx.beginPath(); pctx.ellipse(80, 65, 12, 8, 0, 0, Math.PI*2); pctx.fillStyle = 'rgba(70,140,200,0.9)'; pctx.fill(); pctx.strokeStyle = 'rgba(30,60,90,0.8)'; pctx.lineWidth = 1; pctx.stroke(); pctx.strokeStyle = 'rgba(20,30,40,0.5)'; pctx.lineWidth = 1; pctx.beginPath(); pctx.moveTo(80, 20); pctx.lineTo(80, 120); pctx.moveTo(60, 80); pctx.lineTo(100, 80); pctx.moveTo(40, 60); pctx.lineTo(120, 60); pctx.stroke(); // subtle grime for (let i=0;i<40;i++){ const x = Math.random()*pw, y = Math.random()*ph; pctx.fillStyle = 'rgba(0,0,0,0.03)'; pctx.fillRect(x, y, Math.random()*2, Math.random()*2); } planeReady = true; } createPlaneGraphic();
      
      // Pause menu buttons
      document.getElementById('resumeBtn').onclick = () => togglePause();
      document.getElementById('restartFromPauseBtn').onclick = () => {
        paused = false;
        document.getElementById('pauseMenu').style.display = 'none';
        resetGame();
        startGame();
      };
      document.getElementById('mainMenuBtn').onclick = () => {
        paused = false;
        running = false;
        document.getElementById('pauseMenu').style.display = 'none';
        centerText.style.display = 'block';
      };

      function resetGame() {
        score = 0;
        level = 1;
        lives = 3;
        combo = 0;
        comboTimer = 0;
        player.x = W * 0.5;
        player.y = H * 0.75;
        player.vx = player.vy = 0;
        player.weaponLevel = 1;
        player.fireCooldown = 0;
        player.invuln = 2.0;
        enemies.length = 0;
        bullets.length = 0;
        enemyBullets.length = 0;
        explosions.length = 0;
        powerups.length = 0;
        sparks.length = 0;
        trails.length = 0;
        particles.length = 0;
        spawnTimer = 0;
        spawnInterval = 1.1;
        shakeTime = 0;
        shakeAmp = 0;
        updateUI();
      }

      function updateUI() {
        scoreEl.textContent = score;
        livesEl.textContent = lives;
        weaponEl.textContent = player.weaponLevel <= 1 ? "Cannon" :
          player.weaponLevel === 2 ? "Twin Cannons" :
          player.weaponLevel === 3 ? "Spread" : "Hyper Spread";
        levelEl.textContent = level;
        document.getElementById('highScoreValue').textContent = highScore;
      } // Background draw function drawBackground(dt) { ctx.fillStyle = '#05070c'; ctx.fillRect(0,0,W,H); // Nebula gradient let grd1 = ctx.createRadialGradient(W*0.25, H*0.3, 40, W*0.25, H*0.3, Math.max(W,H)*0.9); grd1.addColorStop(0, 'rgba(40,60,140,0.16)'); grd1.addColorStop(1, 'rgba(0,0,0,0)'); ctx.fillStyle = grd1; ctx.fillRect(0,0,W,H); let grd2 = ctx.createRadialGradient(W*0.75, H*0.1, 30, W*0.75, H*0.1, Math.max(W,H)*0.8); grd2.addColorStop(0, 'rgba(140,40,100,0.12)'); grd2.addColorStop(1, 'rgba(0,0,0,0)'); ctx.fillStyle = grd2; ctx.fillRect(0,0,W,H); function adv(stars, mult=1) { for (let s of stars) { s.y += s.v * dt * mult * (keys['shift']? 1.6:1); if (s.y > H) { s.y -= H; s.x = Math.random()*W; } const tw = (Math.sin(s.tw + time*4) + 1)*0.5; const a = 0.25 + 0.65*tw; ctx.fillStyle = `rgba(${200+Math.floor(55*s.z)}, ${200+Math.floor(40*s.z)}, 255, ${a})`; ctx.fillRect(s.x|0, s.y|0, s.s, s.s); } } adv(starsFar, 1); adv(starsMid, 1.1); adv(starsNear, 1.2); // drifting dust ctx.globalAlpha = 0.4; for (let d of dust) { d.y += 8*dt; if (d.y > H) { d.y = -10; d.x = Math.random()*W; } ctx.fillStyle = `rgba(255,255,255,${d.a})`; ctx.beginPath(); ctx.arc(d.x, d.y, d.r, 0, Math.PI*2); ctx.fill(); } ctx.globalAlpha = 1; } function drawPlayer(dt) { // motion const accel = player.speed * (keys['shift']? player.boostMult : 1); const up = keys['arrowup'] || keys['w']; const down = keys['arrowdown'] || keys['s']; const left = keys['arrowleft'] || keys['a']; const right = keys['arrowright'] || keys['d']; if (left) player.vx -= accel * dt; if (right) player.vx += accel * dt; if (up) player.vy -= accel * dt; if (down) player.vy += accel * dt; player.vx *= player.friction; player.vy *= player.friction; player.x += player.vx * dt; player.y += player.vy * dt; player.x = Math.max(32, Math.min(W-32, player.x)); player.y = Math.max(32, Math.min(H-32, player.y)); // trails (motion blur) trails.push({ x: player.x, y: player.y, r: 26, life: 0.25, t:0 }); while (trails.length > 120) trails.shift(); player.fireCooldown -= dt; if ((keys[' '] || keys['enter']) && player.fireCooldown <= 0) { firePlayerWeapon(); } const roll = Math.max(-0.45, Math.min(0.45, player.vx / 620)); const bob = Math.sin(time*8) * 1.5; // Player heat for glow player.heat = Math.max(0, player.heat - dt*0.8); // Heat haze around engine to heat canvas const fxX = player.x, fxY = player.y + 28; const hazeR = 40 + (keys['shift']?14:0) + Math.abs(roll)*16; heatCtx.globalCompositeOperation = 'lighter'; const hgrad = heatCtx.createRadialGradient(fxX, fxY, 6, fxX, fxY, hazeR); hgrad.addColorStop(0, 'rgba(255,180,80,0.25)'); hgrad.addColorStop(0.5, 'rgba(255,120,40,0.13)'); hgrad.addColorStop(1, 'rgba(0,0,0,0)'); heatCtx.fillStyle = hgrad; heatCtx.beginPath(); heatCtx.arc(fxX, fxY, hazeR, 0, Math.PI*2); heatCtx.fill(); heatCtx.globalCompositeOperation = 'source-over'; // Draw jet ctx.save(); ctx.translate(player.x, player.y + bob); ctx.rotate(roll*0.8); ctx.scale(0.7 + Math.abs(roll)*0.05, 0.7); if (planeReady) { ctx.drawImage(planeCanvas, -planeCanvas.width/2, -planeCanvas.height*0.6); } else { ctx.fillStyle = '#aab7c4'; ctx.beginPath(); ctx.moveTo(0, -30); ctx.lineTo(18, 18); ctx.lineTo(-18, 18); ctx.closePath(); ctx.fill(); } // Engine flame with additive glow const flameLen = 20 + Math.sin(time*40)*6 + (keys['shift']? 10:0); const grad = ctx.createLinearGradient(0, 36, 0, 36 + flameLen); grad.addColorStop(0, 'rgba(255,230,150,0.95)'); grad.addColorStop(0.5, 'rgba(255,160,50,0.8)'); grad.addColorStop(1, 'rgba(255,80,0,0)'); ctx.globalCompositeOperation = 'lighter'; ctx.fillStyle = grad; ctx.beginPath(); ctx.moveTo(-8, 36); ctx.quadraticCurveTo(0, 36 + flameLen*0.2, 8, 36); ctx.quadraticCurveTo(0, 36 + flameLen, -8, 36); ctx.fill(); ctx.globalCompositeOperation = 'source-over'; // Invulnerability blink shield if (player.invuln > 0) { const a = (Math.sin(time*20) > 0) ? 0.5 : 0.2; ctx.strokeStyle = `rgba(120,200,255,${a})`; ctx.lineWidth = 2; ctx.beginPath(); ctx.ellipse(0, 10, 44, 54, 0, 0, Math.PI*2); ctx.stroke(); } ctx.restore(); // Player glow to glow layer glowCtx.globalCompositeOperation = 'lighter'; const glowR = 32 + (keys['shift']?8:0); const g = glowCtx.createRadialGradient(player.x, player.y+12, 4, player.x, player.y+12, glowR); g.addColorStop(0, 'rgba(150,220,255,0.35)'); g.addColorStop(1, 'rgba(0,0,0,0)'); glowCtx.fillStyle = g; glowCtx.beginPath(); glowCtx.arc(player.x, player.y+12, glowR, 0, Math.PI*2); glowCtx.fill(); glowCtx.globalCompositeOperation = 'source-over'; } function firePlayerWeapon() { shootSound(); player.heat = Math.min(1, player.heat + 0.2); player.fireCooldown = Math.max(0.06, player.fireRate * (keys['shift']? 0.8 : 1)); const base = { x: player.x, y: player.y - 34, vx: 0, vy: -980, r: 4, dmg: 1, life: 1.6, t:0 }; const addBullet = (b) => { const nb = Object.assign({}, base, b); nb.col = '#bdf'; bullets.push(nb); // Muzzle flash to glow const mg = glowCtx.createRadialGradient(nb.x, nb.y+8, 2, nb.x, nb.y+8, 18); mg.addColorStop(0, 'rgba(200,240,255,0.8)'); mg.addColorStop(1, 'rgba(0,0,0,0)'); glowCtx.globalCompositeOperation = 'lighter'; glowCtx.beginPath(); glowCtx.arc(nb.x, nb.y+8, 18, 0, Math.PI*2); glowCtx.fillStyle = mg; glowCtx.fill(); glowCtx.globalCompositeOperation = 'source-over'; }; if (player.weaponLevel <= 1) { addBullet({}); } else if (player.weaponLevel === 2) { addBullet({ x: player.x - 12 }); addBullet({ x: player.x + 12 }); } else if (player.weaponLevel === 3) { addBullet({ vx: -140, r: 3 }); addBullet({}); addBullet({ vx: 140, r: 3 }); } else { addBullet({ vx: -200, r: 3 }); addBullet({ vx: -80 }); addBullet({ vx: 0, r: 5, dmg: 2 }); addBullet({ vx: 80 }); addBullet({ vx: 200, r: 3 }); } } function updateBullets(dt) { for (let i=bullets.length-1;i>=0;i--) { const b = bullets[i]; b.t += dt; b.x += b.vx * dt; b.y += b.vy * dt; b.life -= dt; // motion streak to motion layer motionCtx.strokeStyle = 'rgba(180,230,255,0.35)'; motionCtx.lineWidth = 2; motionCtx.beginPath(); motionCtx.moveTo(b.x, b.y + 6); motionCtx.lineTo(b.x, b.y + 16); motionCtx.stroke(); if (b.x < -20 || b.x > W+20 || b.y < -30 || b.life <= 0) { bullets.splice(i,1); } } } function drawBullets() { for (const b of bullets) { const grd = ctx.createLinearGradient(b.x, b.y+6, b.x, b.y-10); grd.addColorStop(0, 'rgba(80,180,255,0.25)'); grd.addColorStop(1, 'rgba(180,240,255,0.95)'); ctx.fillStyle = grd; ctx.beginPath(); ctx.ellipse(b.x, b.y, b.r, b.r*1.4, 0, 0, Math.PI*2); ctx.fill(); // glow layer for bullets glowCtx.globalCompositeOperation = 'lighter'; const g = glowCtx.createRadialGradient(b.x, b.y, 1, b.x, b.y, 14); g.addColorStop(0, 'rgba(150,220,255,0.7)'); g.addColorStop(1, 'rgba(0,0,0,0)'); glowCtx.fillStyle = g; glowCtx.beginPath(); glowCtx.arc(b.x, b.y, 14, 0, Math.PI*2); glowCtx.fill(); glowCtx.globalCompositeOperation = 'source-over'; } } function spawnEnemy() { const kind = Math.random() < 0.6 ? 'basic' : (Math.random()<0.5?'zigzag':'shooter'); let ex = Math.random()* (W-80) + 40; const enemy = { kind, x: ex, y: -40, vx: 0, vy: 70 + Math.random()*50 + level*6, baseHp: (kind==='shooter' ? 6+level : (kind==='zigzag'? 5+level : 3+level*0.7)), hp: 0, fireCd: 1 + Math.random()*0.6, t: 0, w: 34, h: 28 }; enemy.hp = enemy.baseHp; enemies.push(enemy); } function updateEnemies(dt) { spawnTimer -= dt; if (spawnTimer <= 0) { spawnEnemy(); spawnTimer = Math.max(0.4, spawnInterval - level*0.05); } for (let i=enemies.length-1;i>=0;i--) { const e = enemies[i]; e.t += dt; if (e.kind === 'zigzag') { e.x += Math.sin(e.t*3) * 120 * dt; } else if (e.kind === 'basic') { e.x += Math.sin((e.t+e.x*0.01)*2) * 40 * dt; } else if (e.kind === 'shooter') { e.x += Math.sin((e.t*1.7)+ (e.x*0.005)) * 80 * dt; e.fireCd -= dt; if (e.fireCd <= 0) { fireEnemyShot(e); e.fireCd = 1.4 - Math.min(0.8, level*0.05) + Math.random()*0.5; } } e.y += e.vy * dt; if (e.y > H + 60) enemies.splice(i,1); } } function fireEnemyShot(e) { const angle = Math.atan2((player.y - e.y), (player.x - e.x)); const speed = 260 + Math.random()*80 + level*6; const vx = Math.cos(angle)*speed; const vy = Math.sin(angle)*speed; enemyBullets.push({ x: e.x, y: e.y, vx, vy, r: 4, life: 4, color: '#ff7a7a' }); // glow pulse glowCtx.globalCompositeOperation = 'lighter'; const g = glowCtx.createRadialGradient(e.x, e.y, 2, e.x, e.y, 16); g.addColorStop(0, 'rgba(255,150,120,0.7)'); g.addColorStop(1, 'rgba(0,0,0,0)'); glowCtx.fillStyle = g; glowCtx.beginPath(); glowCtx.arc(e.x, e.y, 16, 0, Math.PI*2); glowCtx.fill(); glowCtx.globalCompositeOperation = 'source-over'; beep({freq: 300, type:'square', duration:0.06, attack:0.005, release:0.08}); } function drawEnemies() { for (const e of enemies) { ctx.save(); ctx.translate(e.x, e.y); // body const hull = e.kind==='shooter' ? '#ff9f43' : (e.kind==='zigzag'?'#66d1a7':'#9aa6b2'); ctx.fillStyle = hull; ctx.strokeStyle = 'rgba(0,0,0,0.35)'; ctx.lineWidth = 2; if (!ctx.roundRect) { ctx.beginPath(); ctx.rect(-e.w/2, -e.h/2, e.w, e.h); } else { ctx.beginPath(); ctx.roundRect(-e.w/2, -e.h/2, e.w, e.h, 6); } ctx.fill(); ctx.stroke(); // cockpit ctx.fillStyle = 'rgba(50,120,200,0.85)'; ctx.fillRect(-8, -e.h/2 + 4, 16, 10); // engine glow const eg = ctx.createLinearGradient(0, e.h/2, 0, e.h/2 + 12); eg.addColorStop(0, 'rgba(255,180,80,0.85)'); eg.addColorStop(1, 'rgba(255,100,0,0)'); ctx.fillStyle = eg; ctx.beginPath(); ctx.moveTo(-10, e.h/2); ctx.lineTo(10, e.h/2); ctx.lineTo(0, e.h/2 + 12); ctx.closePath(); ctx.fill(); // health bar const hpPct = Math.max(0, Math.min(1, e.hp / e.baseHp)); ctx.fillStyle = 'rgba(0,0,0,0.5)'; ctx.fillRect(-e.w/2, -e.h/2 - 8, e.w, 4); ctx.fillStyle = hpPct>0.5? '#7CFC00' : (hpPct>0.25? '#FFD700' : '#FF6347'); ctx.fillRect(-e.w/2, -e.h/2 - 8, e.w*hpPct, 4); ctx.restore(); // glow aura glowCtx.globalCompositeOperation = 'lighter'; const g = glowCtx.createRadialGradient(e.x, e.y+6, 2, e.x, e.y+6, 24); g.addColorStop(0, 'rgba(255,180,80,0.25)'); g.addColorStop(1, 'rgba(0,0,0,0)'); glowCtx.fillStyle = g; glowCtx.beginPath(); glowCtx.arc(e.x, e.y+6, 24, 0, Math.PI*2); glowCtx.fill(); glowCtx.globalCompositeOperation = 'source-over'; } } function updateEnemyBullets(dt) { for (let i=enemyBullets.length-1;i>=0;i--) { const b = enemyBullets[i]; b.x += b.vx * dt; b.y += b.vy * dt; b.life -= dt; // faint trail motionCtx.strokeStyle = 'rgba(255,120,120,0.25)'; motionCtx.lineWidth = 2; motionCtx.beginPath(); motionCtx.moveTo(b.x, b.y); motionCtx.lineTo(b.x - b.vx*0.01, b.y - b.vy*0.01); motionCtx.stroke(); if (b.x < -30 || b.x > W+30 || b.y < -30 || b.y > H+30 || b.life <= 0) { enemyBullets.splice(i,1); } } } function drawEnemyBullets() { for (const b of enemyBullets) { const grd = ctx.createRadialGradient(b.x, b.y, 1, b.x, b.y, 10); grd.addColorStop(0, 'rgba(255,120,120,0.95)'); grd.addColorStop(1, 'rgba(255,60,60,0.05)'); ctx.fillStyle = grd; ctx.beginPath(); ctx.arc(b.x, b.y, b.r+1.5, 0, Math.PI*2); ctx.fill(); // glow glowCtx.globalCompositeOperation = 'lighter'; const g = glowCtx.createRadialGradient(b.x, b.y, 1, b.x, b.y, 14); g.addColorStop(0, 'rgba(255,120,120,0.7)'); g.addColorStop(1, 'rgba(0,0,0,0)'); glowCtx.fillStyle = g; glowCtx.beginPath(); glowCtx.arc(b.x, b.y, 14, 0, Math.PI*2); glowCtx.fill(); glowCtx.globalCompositeOperation = 'source-over'; } } function spawnExplosion(x,y,color='#ffb347', big=false) { explodeSound(); explosions.push({ x,y, t:0, life: big?0.9:0.6, color, big }); addShake(big? 12 : 7, big? 0.5 : 0.3); // Spawn explosion particles const particleCount = big ? 25 : 15; for (let i = 0; i < particleCount; i++) { const angle = (Math.PI * 2 / particleCount) * i + Math.random() * 0.5; const speed = (big ? 200 : 150) + Math.random() * 100; particles.push({ x: x, y: y, vx: Math.cos(angle) * speed, vy: Math.sin(angle) * speed, life: 0.6 + Math.random() * 0.4, maxLife: 0.6 + Math.random() * 0.4, color: Math.random() > 0.5 ? color : '#fff', size: big ? 3 + Math.random() * 2 : 2 + Math.random() * 2, type: 'explosion' }); } // heat haze big const hr = big? 120 : 60; heatCtx.globalCompositeOperation = 'lighter'; const hg = heatCtx.createRadialGradient(x, y, 8, x, y, hr); hg.addColorStop(0, 'rgba(255,210,120,0.35)'); hg.addColorStop(0.5, 'rgba(255,130,40,0.18)'); hg.addColorStop(1, 'rgba(0,0,0,0)'); heatCtx.fillStyle = hg; heatCtx.beginPath(); heatCtx.arc(x, y, hr, 0, Math.PI*2); heatCtx.fill(); heatCtx.globalCompositeOperation = 'source-over'; } function updateExplosions(dt) { for (let i=explosions.length-1;i>=0;i--) { const ex = explosions[i]; ex.t += dt; if (ex.t > ex.life) explosions.splice(i,1); } // sparks for (let i=sparks.length-1;i>=0;i--) { const s = sparks[i]; s.vx *= 0.99; s.vy += 60*dt; s.x += s.vx*dt; s.y += s.vy*dt; s.life -= dt; if (s.life <= 0) sparks.splice(i,1); } // trails age for (let i=trails.length-1;i>=0;i--) { const t = trails[i]; t.t += dt; if (t.t >= t.life) trails.splice(i,1); } // update particles for (let i=particles.length-1;i>=0;i--) { const p = particles[i]; p.life -= dt; p.x += p.vx * dt; p.y += p.vy * dt; p.vx *= 0.98; p.vy *= 0.98; if (p.type === 'explosion') { p.vy += 50 * dt; // gravity } if (p.life <= 0 || p.x < -50 || p.x > W+50 || p.y > H+50) { particles.splice(i,1); } } } function drawExplosions() { for (const ex of explosions) { const p = ex.t / ex.life; const r = (ex.big? 28 : 18) + p*(ex.big? 100 : 56); const a = 1 - p; const rg = ctx.createRadialGradient(ex.x, ex.y, 2, ex.x, ex.y, r); rg.addColorStop(0, `rgba(255,240,160,${a})`); rg.addColorStop(0.4, `rgba(255,160,60,${a*0.85})`); rg.addColorStop(1, 'rgba(0,0,0,0)'); ctx.fillStyle = rg; ctx.beginPath(); ctx.arc(ex.x, ex.y, r, 0, Math.PI*2); ctx.fill(); // glow layer bloom glowCtx.globalCompositeOperation = 'lighter'; const gg = glowCtx.createRadialGradient(ex.x, ex.y, 4, ex.x, ex.y, r*1.2); gg.addColorStop(0, `rgba(255,200,120,${a*0.8})`); gg.addColorStop(1, 'rgba(0,0,0,0)'); glowCtx.fillStyle = gg; glowCtx.beginPath(); glowCtx.arc(ex.x, ex.y, r*1.2, 0, Math.PI*2); glowCtx.fill(); glowCtx.globalCompositeOperation = 'source-over'; } // sparks draw ctx.strokeStyle = 'rgba(255,220,160,0.9)'; ctx.lineWidth = 1; ctx.beginPath(); for (const s of sparks) { ctx.moveTo(s.x, s.y); ctx.lineTo(s.x - s.vx*0.04, s.y - s.vy*0.04); } ctx.stroke(); // Draw particles for (const p of particles) { const a = p.life / p.maxLife; ctx.globalAlpha = a; ctx.fillStyle = p.color; ctx.beginPath(); ctx.arc(p.x, p.y, p.size * a, 0, Math.PI*2); ctx.fill(); } ctx.globalAlpha = 1; } function spawnPowerup(x,y) { const kinds = ['weapon','heal','score']; const kind = kinds[Math.floor(Math.random()*kinds.length)]; powerups.push({ x,y, vy: 80, t:0, kind, r: 12 }); // glow ping glowCtx.globalCompositeOperation = 'lighter'; const g = glowCtx.createRadialGradient(x, y, 2, x, y, 22); g.addColorStop(0, 'rgba(200,255,200,0.6)'); g.addColorStop(1, 'rgba(0,0,0,0)'); glowCtx.fillStyle = g; glowCtx.beginPath(); glowCtx.arc(x, y, 22, 0, Math.PI*2); glowCtx.fill(); glowCtx.globalCompositeOperation = 'source-over'; } function updatePowerups(dt) { for (let i=powerups.length-1;i>=0;i--) { const p = powerups[i]; p.t += dt; p.y += p.vy * dt; if (p.y > H + 40) powerups.splice(i,1); } } function drawPowerups() { for (const p of powerups) { ctx.save(); ctx.translate(p.x, p.y); let c = p.kind==='weapon' ? '#7ee787' : (p.kind==='heal' ? '#ffb3c1' : '#ffd166'); ctx.fillStyle = c; ctx.strokeStyle = 'rgba(0,0,0,0.35)'; ctx.lineWidth = 2; ctx.beginPath(); ctx.arc(0,0,p.r,0,Math.PI*2); ctx.fill(); ctx.stroke(); ctx.fillStyle = 'rgba(0,0,0,0.75)'; ctx.font = 'bold 12px system-ui, sans-serif'; ctx.textAlign='center'; ctx.textBaseline='middle'; ctx.fillText(p.kind==='weapon' ? 'W' : (p.kind==='heal'?'H':'S'), 0, 1); ctx.restore(); // glow glowCtx.globalCompositeOperation = 'lighter'; const g = glowCtx.createRadialGradient(p.x, p.y, 2, p.x, p.y, 18); g.addColorStop(0, 'rgba(255,255,180,0.6)'); g.addColorStop(1, 'rgba(0,0,0,0)'); glowCtx.fillStyle = g; glowCtx.beginPath(); glowCtx.arc(p.x, p.y, 18, 0, Math.PI*2); glowCtx.fill(); glowCtx.globalCompositeOperation = 'source-over'; } } // Collision detection function circleRectColl(cx, cy, r, rx, ry, rw, rh) { const dx = Math.max(rx, Math.min(cx, rx+rw)); const dy = Math.max(ry, Math.min(cy, ry+rh)); const dist = (cx-dx)*(cx-dx) + (cy-dy)*(cy-dy); return dist <= r*r; } function handleCollisions() { // bullets vs enemies for (let i=enemies.length-1;i>=0;i--) { const e = enemies[i]; for (let j=bullets.length-1;j>=0;j--) { const b = bullets[j]; if (circleRectColl(b.x, b.y, b.r+1, e.x - e.w/2, e.y - e.h/2, e.w, e.h)) { e.hp -= b.dmg; hitSound(); bullets.splice(j,1); // sparks for (let k=0;k<6;k++){ sparks.push({ x: e.x, y: e.y, vx: (Math.random()-0.5)*220, vy: (Math.random()-0.5)*220, life: 0.3+Math.random()*0.3 }); } // glow ping glowCtx.globalCompositeOperation = 'lighter'; const g = glowCtx.createRadialGradient(e.x, e.y, 2, e.x, e.y, 16); g.addColorStop(0, 'rgba(255,220,120,0.7)'); g.addColorStop(1, 'rgba(0,0,0,0)'); glowCtx.fillStyle = g; glowCtx.beginPath(); glowCtx.arc(e.x, e.y, 16, 0, Math.PI*2); glowCtx.fill(); glowCtx.globalCompositeOperation = 'source-over'; if (e.hp <= 0) { // Combo system comboTimer = 1.5; combo++; const baseScore = 50 + Math.floor(Math.random()*20) + level*5; const comboBonus = combo > 1 ? Math.floor(baseScore * 0.1 * (combo - 1)) : 0; score += baseScore + comboBonus; // Update high score if (score > highScore) { highScore = score; localStorage.setItem('spaceShooterHighScore', highScore); updateUI(); } // Show combo display if (combo > 1) { const comboDisplay = document.getElementById('comboDisplay'); const comboValue = document.getElementById('comboValue'); comboValue.textContent = combo; comboDisplay.style.opacity = '1'; setTimeout(() => { comboDisplay.style.opacity = '0'; }, 1000); } spawnExplosion(e.x, e.y, '#ff9f43', e.kind==='shooter'); // chance for powerup if (Math.random() < 0.18) spawnPowerup(e.x, e.y); enemies.splice(i,1); break; } } } } // player vs enemy bullets const pr = 18; for (let i=enemyBullets.length-1;i>=0;i--) { const b = enemyBullets[i]; const dx = b.x - player.x; const dy = b.y - player.y; if (dx*dx + dy*dy <= (b.r+pr)*(b.r+pr)) { enemyBullets.splice(i,1); if (player.invuln <= 0) playerHit(); addShake(8, 0.25); } } // player vs enemies for (let i=enemies.length-1;i>=0;i--) { const e = enemies[i]; if (circleRectColl(player.x, player.y, pr, e.x - e.w/2, e.y - e.h/2, e.w, e.h)) { spawnExplosion(e.x, e.y, '#ff9f43', true); enemies.splice(i,1); if (player.invuln <= 0) playerHit(); addShake(10, 0.4); } } // player vs powerups for (let i=powerups.length-1;i>=0;i--) { const p = powerups[i]; const dx = p.x - player.x; const dy = p.y - player.y; if (dx*dx + dy*dy <= (p.r+22)*(p.r+22)) { powerups.splice(i,1); powerupSound(); if (p.kind === 'weapon') { player.weaponLevel = Math.min(4, player.weaponLevel+1); } else if (p.kind === 'heal') { if (lives < 5) lives++; } else { score += 200 + level*20; } updateUI(); // little glow glowCtx.globalCompositeOperation = 'lighter'; const g = glowCtx.createRadialGradient(player.x, player.y, 2, player.x, player.y, 26); g.addColorStop(0, 'rgba(200,255,220,0.8)'); g.addColorStop(1, 'rgba(0,0,0,0)'); glowCtx.fillStyle = g; glowCtx.beginPath(); glowCtx.arc(player.x, player.y, 26, 0, Math.PI*2); glowCtx.fill(); glowCtx.globalCompositeOperation = 'source-over'; } } } function playerHit() { lives--; player.invuln = 2.5; spawnExplosion(player.x, player.y, '#add8e6', true); updateUI(); if (lives <= 0) { gameOver(); } else { // reset position player.x = W*0.5; player.y = H*0.75; player.vx = player.vy = 0; } } function gameOver() { running = false; paused = false; const isNewHighScore = score > parseInt(localStorage.getItem('spaceShooterHighScore') || 0); centerText.innerHTML = ` <h1>Game Over</h1> <p>Your Score: ${score}</p> ${isNewHighScore ? '<p style="color: #FFD700;">NEW HIGH SCORE!</p>' : ''} <p>High Score: ${Math.max(score, highScore)}</p> <div class="btn" id="restartBtn">Play Again</div> `; centerText.style.display = 'block'; document.getElementById('restartBtn').onclick = () => { resetGame(); startGame(); }; } function progressDifficulty(dt) { if (Math.floor(time/20)+1 > level) { level = Math.floor(time/20)+1; updateUI(); } } // Post FX composite function compositeFX() { // motion blur fade motionCtx.fillStyle = 'rgba(0,0,0,0.2)'; motionCtx.fillRect(0,0,W,H); // glow pass: blur-ish by drawing scaled ctx.save(); ctx.globalCompositeOperation = 'lighter'; ctx.globalAlpha = 0.7; // draw glow canvas with small blur trick // scale down then up for cheap blur const s = 0.5; ctx.drawImage(glowCanvas, 0, 0, W, H, 0, 0, W, H); ctx.restore(); // heat haze: refractive wobble effect (cheap) ctx.save(); ctx.globalAlpha = 0.15; ctx.globalCompositeOperation = 'lighter'; ctx.drawImage(heatCanvas, 0, 0); ctx.restore(); // motion layer on top with low alpha ctx.save(); ctx.globalAlpha = 0.7; ctx.drawImage(motionCanvas, 0, 0); ctx.restore(); // vignette + chroma edge tint const vgrad = ctx.createRadialGradient(W/2, H/2, Math.min(W,H)*0.2, W/2, H/2, Math.max(W,H)*0.7); vgrad.addColorStop(0, 'rgba(0,0,0,0)'); vgrad.addColorStop(1, 'rgba(0,0,0,0.35)'); ctx.fillStyle = vgrad; ctx.fillRect(0,0,W,H); // faint color edges ctx.globalCompositeOperation = 'screen'; ctx.strokeStyle = 'rgba(80,140,255,0.06)'; ctx.lineWidth = 20; ctx.strokeRect(10,10,W-20,H-20); ctx.globalCompositeOperation = 'source-over'; // fade glow/heat slightly for persistence glowCtx.fillStyle = 'rgba(0,0,0,0.18)'; glowCtx.fillRect(0,0,W,H); heatCtx.fillStyle = 'rgba(0,0,0,0.12)'; heatCtx.fillRect(0,0,W,H); } // Main loop function loop(ts) { if (!running) return; if (paused) return; const dt = Math.min(0.033, (ts - lastTime)/1000); lastTime = ts; time += dt; // Update combo timer if (comboTimer > 0) { comboTimer -= dt; if (comboTimer <= 0) { combo = 0; } } // Screen shake offset let sx = 0, sy = 0; if (shakeTime > 0) { shakeTime -= dt; const p = shakeTime; const a = shakeAmp * (p); sx = (Math.random()*2-1) * a; sy = (Math.random()*2-1) * a; } ctx.save(); ctx.translate(sx, sy); drawBackground(dt); progressDifficulty(dt); player.invuln = Math.max(0, player.invuln - dt); updateEnemies(dt); updateBullets(dt); updateEnemyBullets(dt); updateExplosions(dt); updatePowerups(dt); handleCollisions(); // Trails behind player for (const t of trails) { const a = 1 - (t.t / t.life); ctx.fillStyle = `rgba(150,200,255,${0.12*a})`; ctx.beginPath(); ctx.arc(t.x, t.y, t.r*(0.8 + 0.4*a), 0, Math.PI*2); ctx.fill(); } drawEnemies(); drawEnemyBullets(); drawBullets(); drawPowerups(); drawExplosions(); drawPlayer(dt); ctx.restore(); compositeFX(); requestAnimationFrame(loop); } function startGame() { centerText.style.display = 'none'; running = true; paused = false; lastTime = performance.now(); updateUI(); // resume audio context on user gesture if (audioCtx.state === 'suspended') audioCtx.resume(); // clear FX layers glowCtx.clearRect(0,0,W,H); heatCtx.clearRect(0,0,W,H); motionCtx.clearRect(0,0,W,H); requestAnimationFrame(loop); } // Debug: Check if startBtn exists
console.log('startBtn element:', startBtn);
console.log('startBtn found:', !!startBtn);

startBtn.onclick = () => {
  console.log('Start button clicked!');
  resetGame();
  startGame();
}; // roundRect polyfill if (!CanvasRenderingContext2D.prototype.roundRect) { CanvasRenderingContext2D.prototype.roundRect = function (x, y, w, h, r) { if (w < 2 * r) r = w / 2; if (h < 2 * r) r = h / 2; this.beginPath(); this.moveTo(x+r, y); this.arcTo(x+w, y, x+w, y+h, r); this.arcTo(x+w, y+h, x, y+h, r); this.arcTo(x, y+h, x, y, r); this.arcTo(x, y, x+w, y, r); this.closePath(); return this; } } })(); </script> </body> </html>
