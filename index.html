Below is a complete, single-file HTML Space Shooter game. It uses a realistic airplane graphic rendered via an embedded SVG, includes keyboard controls, sound effects, multiple enemy types, power-ups, explosions, and a simple scoring/health system. Just save as an .html file and open in a modern browser.

Note: No external assets are required. Everything (graphics, sounds) is generated or embedded.

<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8" />
<meta name="viewport" content="width=device-width, initial-scale=1.0"/>
<title>Space Shooter - Single File</title>
<style>
  html, body {
    margin: 0;
    padding: 0;
    background: #05070c;
    color: #e8ecf1;
    font-family: system-ui, Arial, sans-serif;
    overflow: hidden;
  }
  #ui {
    position: absolute;
    top: 8px;
    left: 8px;
    z-index: 10;
    font-size: 14px;
    text-shadow: 0 0 4px #000;
  }
  #ui .row {
    margin: 4px 0;
  }
  #centerText {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 90%;
    max-width: 800px;
    transform: translate(-50%, -50%);
    text-align: center;
    z-index: 20;
    text-shadow: 0 0 8px #000;
  }
  #centerText h1 {
    margin: 0 0 8px 0;
    font-weight: 700;
  }
  #centerText p {
    margin: 8px 0;
  }
  #centerText .btn {
    display: inline-block;
    background: #1f6feb;
    color: white;
    border-radius: 6px;
    padding: 10px 18px;
    margin-top: 12px;
    cursor: pointer;
    user-select: none;
  }
  #centerText .btn:hover {
    background: #2a7bf6;
  }
  canvas {
    display: block;
  }
</style>
</head>
<body>
<div id="ui">
  <div class="row">Score: <span id="score">0</span></div>
  <div class="row">Lives: <span id="lives">3</span></div>
  <div class="row">Weapon: <span id="weapon">Cannon</span></div>
  <div class="row">Level: <span id="level">1</span></div>
</div>
<div id="centerText">
  <h1>Space Shooter</h1>
  <p>Controls: Arrow keys or WASD to move. Space to shoot. Shift for boost. P to pause.</p>
  <p>Survive waves, collect power-ups, and score as high as you can.</p>
  <div class="btn" id="startBtn">Start Game</div>
</div>
<canvas id="game"></canvas>

<script>
(function() {
  const canvas = document.getElementById('game');
  const ctx = canvas.getContext('2d', { alpha: false });
  let W = window.innerWidth, H = window.innerHeight;
  canvas.width = W; canvas.height = H;

  // UI references
  const scoreEl = document.getElementById('score');
  const livesEl = document.getElementById('lives');
  const weaponEl = document.getElementById('weapon');
  const levelEl = document.getElementById('level');
  const startBtn = document.getElementById('startBtn');
  const centerText = document.getElementById('centerText');

  // Game state
  let running = false;
  let paused = false;
  let lastTime = 0;
  let time = 0;
  let score = 0;
  let level = 1;
  let lives = 3;

  // Inputs
  const keys = {};
  window.addEventListener('keydown', (e) => {
    keys[e.key.toLowerCase()] = true;
    if (e.key === ' ' || e.code === 'Space') keys[' '] = true;
    if (e.key.toLowerCase() === 'p') togglePause();
  });
  window.addEventListener('keyup', (e) => {
    keys[e.key.toLowerCase()] = false;
    if (e.key === ' ' || e.code === 'Space') keys[' '] = false;
  });

  window.addEventListener('resize', () => {
    W = window.innerWidth; H = window.innerHeight;
    canvas.width = W; canvas.height = H;
  });

  function togglePause() {
    if (!running) return;
    paused = !paused;
    if (!paused) {
      lastTime = performance.now();
      requestAnimationFrame(loop);
    }
  }

  // Audio (WebAudio minimal synth)
  const audioCtx = new (window.AudioContext || window.webkitAudioContext)();
  let masterGain = audioCtx.createGain();
  masterGain.gain.value = 0.2;
  masterGain.connect(audioCtx.destination);

  function beep({freq=440, type='square', duration=0.08, attack=0.005, release=0.08, detune=0, pan=0}) {
    const osc = audioCtx.createOscillator();
    const gain = audioCtx.createGain();
    const panner = (audioCtx.createStereoPanner) ? audioCtx.createStereoPanner() : null;
    osc.type = type;
    osc.frequency.value = freq;
    osc.detune.value = detune;
    gain.gain.value = 0;
    if (panner) panner.pan.value = pan;

    osc.connect(gain);
    if (panner) {
      gain.connect(panner);
      panner.connect(masterGain);
    } else {
      gain.connect(masterGain);
    }

    const now = audioCtx.currentTime;
    gain.gain.cancelScheduledValues(now);
    gain.gain.setValueAtTime(0, now);
    gain.gain.linearRampToValueAtTime(0.5, now + attack);
    gain.gain.linearRampToValueAtTime(0.0, now + attack + release);

    osc.start(now);
    osc.stop(now + duration + 0.05);
  }

  function shootSound() {
    beep({freq: 880, type:'sawtooth', duration:0.06, attack:0.002, release:0.06});
  }
  function powerupSound() {
    beep({freq: 660, type:'triangle', duration:0.12, attack:0.005, release:0.14});
  }
  function hitSound() {
    beep({freq: 220, type:'square', duration:0.08, attack:0.005, release:0.1});
  }
  function explodeSound() {
    // noisy burst via detune sweep
    beep({freq: 80, type:'sawtooth', duration:0.2, attack:0.005, release:0.2, detune: -1200});
    beep({freq: 120, type:'sawtooth', duration:0.2, attack:0.005, release:0.2, detune: -900});
  }

  // Background starfield
  const stars = [];
  for (let i=0;i<200;i++){
    stars.push({
      x: Math.random()*W,
      y: Math.random()*H,
      z: Math.random()*1 + 0.2,
      s: Math.random()*1.5+0.3,
      c: Math.random()*0.4+0.6
    });
  }

  // Player
  const player = {
    x: W*0.5,
    y: H*0.75,
    vx: 0,
    vy: 0,
    speed: 400,
    boostMult: 1.6,
    friction: 0.9,
    w: 64,
    h: 64,
    fireCooldown: 0,
    fireRate: 0.16,
    weaponLevel: 1,
    invuln: 0
  };

  // Bullets, enemies, explosions, powerups
  const bullets = [];
  const enemyBullets = [];
  const enemies = [];
  const explosions = [];
  const powerups = [];

  // Level/wave management
  let spawnTimer = 0;
  let spawnInterval = 1.2;

  function resetGame() {
    score = 0;
    level = 1;
    lives = 3;
    player.x = W*0.5;
    player.y = H*0.75;
    player.vx = player.vy = 0;
    player.weaponLevel = 1;
    player.fireCooldown = 0;
    player.invuln = 2.0;
    enemies.length = 0;
    bullets.length = 0;
    enemyBullets.length = 0;
    explosions.length = 0;
    powerups.length = 0;
    spawnTimer = 0;
    spawnInterval = 1.2;
    updateUI();
  }

  function updateUI() {
    scoreEl.textContent = score;
    livesEl.textContent = lives;
    weaponEl.textContent = player.weaponLevel <= 1 ? "Cannon" :
                           player.weaponLevel === 2 ? "Twin Cannons" :
                           player.weaponLevel === 3 ? "Spread" : "Hyper Spread";
    levelEl.textContent = level;
  }

  // Drawing helpers
  function drawStarfield(dt) {
    ctx.fillStyle = '#05070c';
    ctx.fillRect(0,0,W,H);
    for (let s of stars) {
      s.y += 20 * s.z * dt * (keys['shift']? 2:1);
      if (s.y > H) { s.y -= H; s.x = Math.random()*W; }
      const alpha = 0.3 + 0.7*s.z;
      ctx.fillStyle = `rgba(${Math.floor(160*s.c)},${Math.floor(180*s.c)},${Math.floor(255*s.c)},${alpha})`;
      ctx.fillRect(s.x|0, s.y|0, s.s, s.s);
    }
    // subtle nebula gradient
    let grd = ctx.createRadialGradient(W*0.3, H*0.2, 50, W*0.3, H*0.2, Math.max(W,H)*0.8);
    grd.addColorStop(0, 'rgba(50,60,110,0.15)');
    grd.addColorStop(1, 'rgba(0,0,0,0)');
    ctx.fillStyle = grd;
    ctx.fillRect(0,0,W,H);
  }

  // Realistic airplane graphic using inline SVG path rendered to offscreen canvas
  let planeCanvas = document.createElement('canvas');
  let planeReady = false;
  function createPlaneGraphic() {
    const pw = 160, ph = 160;
    planeCanvas.width = pw; planeCanvas.height = ph;
    const pctx = planeCanvas.getContext('2d');

    // Draw a realistic jet silhouette via SVG path
    const svgPath = new Path2D("M80 5 L90 45 L110 55 L155 60 L115 70 L100 75 L100 110 L120 130 L110 135 L95 125 L80 150 L65 125 L50 135 L40 130 L60 110 L60 75 L45 70 L5 60 L50 55 L70 45 Z");
    // Metallic gradient
    let grad = pctx.createLinearGradient(0,0,0,ph);
    grad.addColorStop(0, '#cfd8e3');
    grad.addColorStop(0.5, '#8a99ad');
    grad.addColorStop(1, '#c7d1dd');
    pctx.fillStyle = grad;
    pctx.strokeStyle = '#2c3e50';
    pctx.lineWidth = 2;
    pctx.shadowColor = 'rgba(0,0,0,0.25)';
    pctx.shadowBlur = 8;
    pctx.shadowOffsetY = 3;
    pctx.fill(svgPath);
    pctx.shadowColor = 'transparent';
    pctx.stroke(svgPath);

    // cockpit canopy
    pctx.beginPath();
    pctx.ellipse(80, 65, 12, 8, 0, 0, Math.PI*2);
    pctx.fillStyle = 'rgba(70,140,200,0.9)';
    pctx.fill();
    pctx.strokeStyle = 'rgba(30,60,90,0.8)';
    pctx.lineWidth = 1;
    pctx.stroke();

    // panel lines
    pctx.strokeStyle = 'rgba(20,30,40,0.5)';
    pctx.lineWidth = 1;
    pctx.beginPath();
    pctx.moveTo(80, 20); pctx.lineTo(80, 120);
    pctx.moveTo(60, 80); pctx.lineTo(100, 80);
    pctx.moveTo(40, 60); pctx.lineTo(120, 60);
    pctx.stroke();

    // engine glow base
    const flame = pctx.createRadialGradient(80, 150, 2, 80, 150, 20);
    flame.addColorStop(0, 'rgba(255, 200, 80, 0.9)');
    flame.addColorStop(1, 'rgba(255, 120, 0, 0)');
    pctx.fillStyle = flame;
    pctx.beginPath();
    pctx.arc(80, 150, 16, 0, Math.PI*2);
    pctx.fill();

    planeReady = true;
  }
  createPlaneGraphic();

  function drawPlayer(dt) {
    // motion
    const accel = player.speed * (keys['shift']? player.boostMult : 1);
    const up = keys['arrowup'] || keys['w'];
    const down = keys['arrowdown'] || keys['s'];
    const left = keys['arrowleft'] || keys['a'];
    const right = keys['arrowright'] || keys['d'];

    if (left) player.vx -= accel * dt;
    if (right) player.vx += accel * dt;
    if (up) player.vy -= accel * dt;
    if (down) player.vy += accel * dt;

    player.vx *= player.friction;
    player.vy *= player.friction;
    player.x += player.vx * dt;
    player.y += player.vy * dt;

    // bounds
    player.x = Math.max(32, Math.min(W-32, player.x));
    player.y = Math.max(32, Math.min(H-32, player.y));

    // shoot
    player.fireCooldown -= dt;
    if ((keys[' '] || keys['enter']) && player.fireCooldown <= 0) {
      firePlayerWeapon();
    }

    // draw jet
    const roll = Math.max(-0.4, Math.min(0.4, player.vx / 600));
    const bob = Math.sin(time*8) * 1.5;
    ctx.save();
    ctx.translate(player.x, player.y + bob);
    ctx.rotate(0); // nose up
    ctx.scale(0.7 + Math.abs(roll)*0.05, 0.7);
    ctx.rotate(roll);
    if (planeReady) {
      ctx.drawImage(planeCanvas, -planeCanvas.width/2, -planeCanvas.height*0.6);
    } else {
      // fallback triangle
      ctx.fillStyle = '#aab7c4';
      ctx.beginPath();
      ctx.moveTo(0, -30);
      ctx.lineTo(18, 18);
      ctx.lineTo(-18, 18);
      ctx.closePath();
      ctx.fill();
    }

    // engine flame animated
    const flameLen = 20 + Math.sin(time*40)*6 + (keys['shift']? 10:0);
    const grad = ctx.createLinearGradient(0, 0, 0, flameLen);
    grad.addColorStop(0, 'rgba(255,230,150,0.9)');
    grad.addColorStop(0.5, 'rgba(255,160,50,0.7)');
    grad.addColorStop(1, 'rgba(255,80,0,0)');
    ctx.fillStyle = grad;
    ctx.beginPath();
    ctx.moveTo(-8, 36);
    ctx.quadraticCurveTo(0, 36 + flameLen*0.2, 8, 36);
    ctx.quadraticCurveTo(0, 36 + flameLen, -8, 36);
    ctx.fill();

    // invulnerability blink
    if (player.invuln > 0) {
      const a = (Math.sin(time*20) > 0) ? 0.6 : 0.2;
      ctx.fillStyle = `rgba(120,200,255,${a})`;
      ctx.beginPath();
      ctx.ellipse(0, 10, 40, 50, 0, 0, Math.PI*2);
      ctx.fill();
    }
    ctx.restore();
  }

  function firePlayerWeapon() {
    shootSound();
    player.fireCooldown = Math.max(0.06, player.fireRate * (keys['shift']? 0.8 : 1));
    // create bullets based on weapon level
    const base = { x: player.x, y: player.y - 30, vx: 0, vy: -900, r: 4, dmg: 1, life: 1.6, color: '#9bdcff' };
    const addBullet = (b) => bullets.push(Object.assign({}, base, b));

    if (player.weaponLevel <= 1) {
      addBullet({});
    } else if (player.weaponLevel === 2) {
      addBullet({ x: player.x - 12 }); addBullet({ x: player.x + 12 });
    } else if (player.weaponLevel === 3) {
      addBullet({ vx: -120, r: 3 }); addBullet({}); addBullet({ vx: 120, r: 3 });
    } else {
      addBullet({ vx: -180, r: 3 }); addBullet({ vx: -60 });
      addBullet({ vx: 0, r: 5, dmg: 2 });
      addBullet({ vx: 60 }); addBullet({ vx: 180, r: 3 });
    }
  }

  function updateBullets(dt) {
    for (let i=bullets.length-1;i>=0;i--) {
      const b = bullets[i];
      b.x += b.vx * dt;
      b.y += b.vy * dt;
      b.life -= dt;
      if (b.x < -20 || b.x > W+20 || b.y < -30 || b.life <= 0) {
        bullets.splice(i,1);
      }
    }
  }

  function drawBullets() {
    for (const b of bullets) {
      const grd = ctx.createLinearGradient(b.x, b.y+6, b.x, b.y-10);
      grd.addColorStop(0, 'rgba(80,180,255,0.2)');
      grd.addColorStop(1, 'rgba(180,240,255,0.9)');
      ctx.fillStyle = grd;
      ctx.beginPath();
      ctx.ellipse(b.x, b.y, b.r, b.r*1.4, 0, 0, Math.PI*2);
      ctx.fill();
      // trail
      ctx.fillStyle = 'rgba(150, 220, 255, 0.2)';
      ctx.fillRect(b.x-1, b.y, 2, 12);
    }
  }

  function spawnEnemy() {
    const kind = Math.random() < 0.6 ? 'basic' : (Math.random()<0.5?'zigzag':'shooter');
    let ex = Math.random()* (W-80) + 40;
    const enemy = {
      kind,
      x: ex,
      y: -40,
      vx: 0,
      vy: 70 + Math.random()*50 + level*6,
      hp: kind==='shooter' ? 6+level : (kind==='zigzag'? 5+level : 3+level*0.7),
      fireCd: 1 + Math.random()*0.6,
      t: 0,
      w: 34, h: 28
    };
    enemies.push(enemy);
  }

  function updateEnemies(dt) {
    spawnTimer -= dt;
    if (spawnTimer <= 0) {
      spawnEnemy();
      spawnTimer = Math.max(0.4, spawnInterval - level*0.05);
    }

    for (let i=enemies.length-1;i>=0;i--) {
      const e = enemies[i];
      e.t += dt;
      if (e.kind === 'zigzag') {
        e.x += Math.sin(e.t*3) * 120 * dt;
      } else if (e.kind === 'basic') {
        e.x += Math.sin((e.t+e.x*0.01)*2) * 40 * dt;
      } else if (e.kind === 'shooter') {
        e.x += Math.sin((e.t*1.7)+ (e.x*0.005)) * 80 * dt;
        e.fireCd -= dt;
        if (e.fireCd <= 0) {
          fireEnemyShot(e);
          e.fireCd = 1.4 - Math.min(0.8, level*0.05) + Math.random()*0.5;
        }
      }
      e.y += e.vy * dt;
      if (e.y > H + 60) enemies.splice(i,1);
    }
  }

  function fireEnemyShot(e) {
    const angle = Math.atan2((player.y - e.y), (player.x - e.x));
    const speed = 260 + Math.random()*80 + level*6;
    const vx = Math.cos(angle)*speed;
    const vy = Math.sin(angle)*speed;
    enemyBullets.push({ x: e.x, y: e.y, vx, vy, r: 4, life: 4, color: '#ff7a7a' });
    beep({freq: 300, type:'square', duration:0.06, attack:0.005, release:0.08});
  }

  function drawEnemies() {
    for (const e of enemies) {
      ctx.save();
      ctx.translate(e.x, e.y);
      // body
      const hull = e.kind==='shooter' ? '#ff9f43' : (e.kind==='zigzag'?'#66d1a7':'#9aa6b2');
      ctx.fillStyle = hull;
      ctx.strokeStyle = 'rgba(0,0,0,0.35)';
      ctx.lineWidth = 2;
      ctx.beginPath();
      ctx.roundRect(-e.w/2, -e.h/2, e.w, e.h, 6);
      ctx.fill();
      ctx.stroke();
      // cockpit
      ctx.fillStyle = 'rgba(50,120,200,0.8)';
      ctx.fillRect(-8, -e.h/2 + 4, 16, 10);
      // engine glow
      const eg = ctx.createLinearGradient(0, e.h/2, 0, e.h/2 + 12);
      eg.addColorStop(0, 'rgba(255,180,80,0.8)');
      eg.addColorStop(1, 'rgba(255,100,0,0)');
      ctx.fillStyle = eg;
      ctx.beginPath();
      ctx.moveTo(-10, e.h/2);
      ctx.lineTo(10, e.h/2);
      ctx.lineTo(0, e.h/2 + 12);
      ctx.closePath();
      ctx.fill();
      // health bar
      const hpPct = Math.max(0, Math.min(1, e.hp / (e.kind==='shooter'?(6+level):(e.kind==='zigzag'?(5+level):(3+level*0.7)))));
      ctx.fillStyle = 'rgba(0,0,0,0.4)';
      ctx.fillRect(-e.w/2, -e.h/2 - 8, e.w, 4);
      ctx.fillStyle = hpPct>0.5? '#7CFC00' : (hpPct>0.25? '#FFD700' : '#FF6347');
      ctx.fillRect(-e.w/2, -e.h/2 - 8, e.w*hpPct, 4);
      ctx.restore();
    }
  }

  function updateEnemyBullets(dt) {
    for (let i=enemyBullets.length-1;i>=0;i--) {
      const b = enemyBullets[i];
      b.x += b.vx * dt;
      b.y += b.vy * dt;
      b.life -= dt;
      if (b.x < -30 || b.x > W+30 || b.y < -30 || b.y > H+30 || b.life <= 0) {
        enemyBullets.splice(i,1);
      }
    }
  }

  function drawEnemyBullets() {
    for (const b of enemyBullets) {
      const grd = ctx.createRadialGradient(b.x, b.y, 1, b.x, b.y, 10);
      grd.addColorStop(0, 'rgba(255,120,120,0.95)');
      grd.addColorStop(1, 'rgba(255,60,60,0.05)');
      ctx.fillStyle = grd;
      ctx.beginPath();
      ctx.arc(b.x, b.y, b.r+1.5, 0, Math.PI*2);
      ctx.fill();
    }
  }

  function spawnExplosion(x,y,color='#ffb347') {
    explodeSound();
    explosions.push({
      x,y, t:0, life:0.6, color
    });
  }

  function updateExplosions(dt) {
    for (let i=explosions.length-1;i>=0;i--) {
      const ex = explosions[i];
      ex.t += dt;
      if (ex.t > ex.life) explosions.splice(i,1);
    }
  }
  function drawExplosions() {
    for (const ex of explosions) {
      const p = ex.t / ex.life;
      const r = 18 + p*56;
      const a = 1 - p;
      const rg = ctx.createRadialGradient(ex.x, ex.y, 2, ex.x, ex.y, r);
      rg.addColorStop(0, `rgba(255,240,160,${a})`);
      rg.addColorStop(0.4, `rgba(255,160,60,${a*0.8})`);
      rg.addColorStop(1, 'rgba(0,0,0,0)');
      ctx.fillStyle = rg;
      ctx.beginPath();
      ctx.arc(ex.x, ex.y, r, 0, Math.PI*2);
      ctx.fill();
      // debris sparks
      ctx.strokeStyle = `rgba(255,220,160,${a*0.9})`;
      ctx.lineWidth = 1;
      ctx.beginPath();
      for (let k=0;k<8;k++){
        const ang = (k/8)*Math.PI*2;
        const rr = r * (0.3 + 0.7*Math.random());
        ctx.moveTo(ex.x, ex.y);
        ctx.lineTo(ex.x + Math.cos(ang)*rr, ex.y + Math.sin(ang)*rr);
      }
      ctx.stroke();
    }
  }

  function spawnPowerup(x,y) {
    const kinds = ['weapon','heal','score'];
    const kind = kinds[Math.floor(Math.random()*kinds.length)];
    powerups.push({
      x,y, vy: 80, t:0, kind, r: 12
    });
  }

  function updatePowerups(dt) {
    for (let i=powerups.length-1;i>=0;i--) {
      const p = powerups[i];
      p.t += dt;
      p.y += p.vy * dt;
      if (p.y > H + 40) powerups.splice(i,1);
    }
  }

  function drawPowerups() {
    for (const p of powerups) {
      ctx.save();
      ctx.translate(p.x, p.y);
      let c = p.kind==='weapon' ? '#7ee787' : (p.kind==='heal' ? '#ffb3c1' : '#ffd166');
      ctx.fillStyle = c;
      ctx.strokeStyle = 'rgba(0,0,0,0.35)';
      ctx.lineWidth = 2;
      ctx.beginPath();
      ctx.arc(0,0,p.r,0,Math.PI*2);
      ctx.fill();
      ctx.stroke();
      ctx.fillStyle = 'rgba(0,0,0,0.7)';
      ctx.font = 'bold 12px system-ui, sans-serif';
      ctx.textAlign='center';
      ctx.textBaseline='middle';
      ctx.fillText(p.kind==='weapon' ? 'W' : (p.kind==='heal'?'H':'S'), 0, 1);
      ctx.restore();
    }
  }

  // Collision detection
  function circleRectColl(cx, cy, r, rx, ry, rw, rh) {
    const dx = Math.max(rx, Math.min(cx, rx+rw));
    const dy = Math.max(ry, Math.min(cy, ry+rh));
    const dist = (cx-dx)*(cx-dx) + (cy-dy)*(cy-dy);
    return dist <= r*r;
  }

  function handleCollisions() {
    // bullets vs enemies
    for (let i=enemies.length-1;i>=0;i--) {
      const e = enemies[i];
      for (let j=bullets.length-1;j>=0;j--) {
        const b = bullets[j];
        if (circleRectColl(b.x, b.y, b.r+1, e.x - e.w/2, e.y - e.h/2, e.w, e.h)) {
          e.hp -= b.dmg;
          hitSound();
          bullets.splice(j,1);
          if (e.hp <= 0) {
            score += 50 + Math.floor(Math.random()*20) + level*5;
            spawnExplosion(e.x, e.y);
            // small chance for powerup
            if (Math.random() < 0.18) spawnPowerup(e.x, e.y);
            enemies.splice(i,1);
            break;
          }
        }
      }
    }

    // player vs enemy bullets
    const pr = 18;
    for (let i=enemyBullets.length-1;i>=0;i--) {
      const b = enemyBullets[i];
      const dx = b.x - player.x;
      const dy = b.y - player.y;
      if (dx*dx + dy*dy <= (b.r+pr)*(b.r+pr)) {
        enemyBullets.splice(i,1);
        if (player.invuln <= 0) playerHit();
      }
    }

    // player vs enemies
    for (let i=enemies.length-1;i>=0;i--) {
      const e = enemies[i];
      if (circleRectColl(player.x, player.y, pr, e.x - e.w/2, e.y - e.h/2, e.w, e.h)) {
        spawnExplosion(e.x, e.y, '#ff9f43');
        enemies.splice(i,1);
        if (player.invuln <= 0) playerHit();
      }
    }

    // player vs powerups
    for (let i=powerups.length-1;i>=0;i--) {
      const p = powerups[i];
      const dx = p.x - player.x;
      const dy = p.y - player.y;
      if (dx*dx + dy*dy <= (p.r+22)*(p.r+22)) {
        powerups.splice(i,1);
        powerupSound();
        if (p.kind === 'weapon') {
          player.weaponLevel = Math.min(4, player.weaponLevel+1);
        } else if (p.kind === 'heal') {
          if (lives < 5) lives++;
        } else {
          score += 200 + level*20;
        }
        updateUI();
      }
    }
  }

  function playerHit() {
    lives--;
    player.invuln = 2.5;
    spawnExplosion(player.x, player.y, '#add8e6');
    if (lives <= 0) {
      gameOver();
    } else {
      // reset position
      player.x = W*0.5; player.y = H*0.75;
      player.vx = player.vy = 0;
      updateUI();
    }
  }

  function gameOver() {
    running = false;
    paused = false;
    centerText.innerHTML = `
      <h1>Game Over</h1>
      <p>Your Score: ${score}</p>
      <div class="btn" id="restartBtn">Play Again</div>
    `;
    centerText.style.display = 'block';
    document.getElementById('restartBtn').onclick = () => {
      resetGame();
      startGame();
    };
  }

  function progressDifficulty(dt) {
    // Increase level with time and score
    if (Math.floor(time/20)+1 > level) {
      level = Math.floor(time/20)+1;
      updateUI();
    }
  }

  // Main loop
  function loop(ts) {
    if (!running) return;
    if (paused) return;

    const dt = Math.min(0.033, (ts - lastTime)/1000);
    lastTime = ts;
    time += dt;

    drawStarfield(dt);
    progressDifficulty(dt);

    player.invuln = Math.max(0, player.invuln - dt);

    updateEnemies(dt);
    updateBullets(dt);
    updateEnemyBullets(dt);
    updateExplosions(dt);
    updatePowerups(dt);

    handleCollisions();

    drawEnemies();
    drawEnemyBullets();
    drawBullets();
    drawPowerups();
    drawExplosions();
    drawPlayer(dt);

    requestAnimationFrame(loop);
  }

  function startGame() {
    centerText.style.display = 'none';
    running = true; paused = false;
    lastTime = performance.now();
    // resume audio context on user gesture
    if (audioCtx.state === 'suspended') audioCtx.resume();
    requestAnimationFrame(loop);
  }

  startBtn.onclick = () => {
    resetGame();
    startGame();
  };

  // Add roundRect polyfill for older browsers
  if (!CanvasRenderingContext2D.prototype.roundRect) {
    CanvasRenderingContext2D.prototype.roundRect = function (x, y, w, h, r) {
      if (w < 2 * r) r = w / 2;
      if (h < 2 * r) r = h / 2;
      this.beginPath();
      this.moveTo(x+r, y);
      this.arcTo(x+w, y,   x+w, y+h, r);
      this.arcTo(x+w, y+h, x,   y+h, r);
      this.arcTo(x,   y+h, x,   y,   r);
      this.arcTo(x,   y,   x+w, y,   r);
      this.closePath();
      return this;
    }
  }
})();
</script>
</body>
</html>



